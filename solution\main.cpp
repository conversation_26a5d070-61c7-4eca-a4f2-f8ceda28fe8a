#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX

using namespace std;

// Dynamic move penalty calculation parameters
const double BASE_MOVE_PENALTY = 50.0;
const double URGENCY_THRESHOLD = 0.8; // When urgency > 0.8, reduce move penalty

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;

    void add_task(int start_time, int finish_time, int mem_needed) {
        // Inserts events while maintaining sort order by time.
        // lower_bound finds the first element not less than the given value.
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, {start_time, mem_needed});

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, {finish_time, -mem_needed});
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1; // Using -1 to represent infinity/failure
        }

        int current_mem_usage = 0; // Changed from long long to int
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            } else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        // If not enough memory, check future event points for released memory
        // Find the first event that happens after arrival_time
        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        for (auto i = it; i != events.end(); ++i) {
            current_mem_usage += i->second;
            if (current_mem_usage + mem_needed <= m) {
                return i->first; // Found a slot starting at this event's time
            }
        }
        return -1; // No suitable slot found
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    // (time, server_id, npu_id_in_server, batch_size)
    vector<tuple<int, int, int, int>> requests;

    // Calculate urgency based on current time and time window
    double get_urgency(long long current_time) const {
        if (e <= s) return 1.0; // Edge case
        return max(0.0, min(1.0, (double)(current_time - s) / (e - s)));
    }

    // Calculate remaining time ratio
    double get_remaining_time_ratio(long long current_time) const {
        if (e <= current_time) return 0.0;
        if (current_time <= s) return 1.0;
        return (double)(e - current_time) / (e - s);
    }
};


// --- Helper Functions ---

// Calculate correct inference time according to the problem statement
int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    double f_b = k * sqrt(batch_size);
    return static_cast<int>(ceil(batch_size / f_b));
}

// Calculate efficiency (samples per unit time) for a given batch size
double calculate_efficiency(int batch_size, int k) {
    if (batch_size <= 0) return 0.0;
    int inference_time = calculate_inference_time(batch_size, k);
    return inference_time > 0 ? (double)batch_size / inference_time : 0.0;
}

// Find optimal batch size considering efficiency and constraints
int find_optimal_batch_size(int max_batch, int samples_left, int k, long long remaining_time) {
    int best_batch = 1;
    double best_score = -1.0;

    for (int batch = 1; batch <= min(max_batch, samples_left); ++batch) {
        int inference_time = calculate_inference_time(batch, k);

        // Skip if inference time exceeds remaining time
        if (inference_time > remaining_time) continue;

        double efficiency = calculate_efficiency(batch, k);
        // Prefer larger batches when efficiency is similar (within 5%)
        double batch_bonus = 1.0 + 0.1 * (double)batch / max_batch;
        double score = efficiency * batch_bonus;

        if (score > best_score) {
            best_score = score;
            best_batch = batch;
        }
    }

    return best_batch;
}

// Calculate dynamic move penalty based on user urgency
double calculate_move_penalty(const User& user, long long current_time) {
    double urgency = user.get_urgency(current_time);
    if (urgency > URGENCY_THRESHOLD) {
        // Reduce move penalty for urgent users
        return BASE_MOVE_PENALTY * (1.0 - (urgency - URGENCY_THRESHOLD) / (1.0 - URGENCY_THRESHOLD) * 0.5);
    }
    return BASE_MOVE_PENALTY;
}

// --- Main Logic ---

int main() {
    // Fast I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    // 1. Read Input
    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    // 2. Initialize Models
    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({npu_counter++, server.id, server.k, server.m, {}});
        }
    }

    // 3. Event-driven Scheduling with improved prioritization
    // Priority queue with urgency-based scheduling
    // Stores { -priority_score, user_id } where higher priority_score means more urgent
    priority_queue<pair<double, int>> user_pq;
    for (const auto& user : users) {
        // Initial priority based on urgency and next send time
        double urgency = user.get_urgency(user.next_send_time);
        double priority_score = urgency * 1000.0 - user.next_send_time * 0.001;
        user_pq.push({priority_score, user.id});
    }

    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        double priority_score = top.first;
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        long long send_time = user.next_send_time;

        // --- Decision Making with improved cost function ---
        double best_cost = -1;
        int best_finish_time = -1;
        int best_npu_idx = -1;
        int best_batch_size = -1;

        // Calculate user's remaining time and urgency
        long long remaining_time = max(0LL, (long long)user.e - send_time);
        double urgency = user.get_urgency(send_time);

        for (int i = 0; i < npus.size(); ++i) {
            const auto& npu = npus[i];
            const auto& server = servers_data[npu.server_id];

            if (server.m <= B) continue;
            int max_b_for_npu = (server.m - B) / A;
            if (max_b_for_npu <= 0) continue;

            // Find optimal batch size considering efficiency and time constraints
            int optimal_batch = find_optimal_batch_size(max_b_for_npu, user.samples_left, server.k, remaining_time);
            int mem_needed = A * optimal_batch + B;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            // Use correct inference time calculation
            int inference_time = calculate_inference_time(optimal_batch, server.k);
            int finish_time = start_time + inference_time;

            // Skip if this would exceed user's deadline by too much
            if (finish_time > user.e + (user.e - user.s) * 0.5) continue;

            // Improved cost function
            double normalized_finish_time = (double)(finish_time - user.s) / (user.e - user.s);
            double move_penalty = 0.0;
            if (user.last_npu_id != -1 && npu.id != user.last_npu_id) {
                move_penalty = calculate_move_penalty(user, send_time);
            }

            // Cost components: finish time (70%), move penalty (20%), urgency factor (10%)
            double cost = 0.7 * normalized_finish_time + 0.2 * (move_penalty / 100.0) + 0.1 * urgency;

            if (best_cost == -1 || cost < best_cost) {
                best_cost = cost;
                best_finish_time = finish_time;
                best_npu_idx = i;
                best_batch_size = optimal_batch;
            }
        }

        // --- Commit to the best decision ---
        if (best_npu_idx != -1) {
            NPU& chosen_npu = npus[best_npu_idx];
            int batch = best_batch_size;

            int mem_needed = A * batch + B;
            int latency = latencies[chosen_npu.server_id][user.id];
            int server_k = servers_data[chosen_npu.server_id].k;
            int inference_time = calculate_inference_time(batch, server_k); // Use correct calculation
            int start_time = best_finish_time - inference_time;

            chosen_npu.add_task(start_time, best_finish_time, mem_needed);

            int npu_id_in_server;
            int base_npu_id = 0;
            for(int i = 0; i < chosen_npu.server_id; ++i) {
                base_npu_id += servers_data[i].g;
            }
            npu_id_in_server = chosen_npu.id - base_npu_id + 1;

            user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

            user.samples_left -= batch;
            user.next_send_time = send_time + latency + 1;
            user.last_npu_id = chosen_npu.id;

            if (user.samples_left > 0) {
                // Update priority based on new urgency
                double new_urgency = user.get_urgency(user.next_send_time);
                double new_priority_score = new_urgency * 1000.0 - user.next_send_time * 0.001;
                user_pq.push({new_priority_score, user.id});
            }
        } else {
            // If no suitable NPU found, try again later with higher priority
            if (user.samples_left > 0) {
                double emergency_priority = 2000.0 - send_time * 0.001; // High priority
                user_pq.push({emergency_priority, user.id});
            }
        }
    }

    // 4. Print Output
    for (auto& user : users) {
        sort(user.requests.begin(), user.requests.end());
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                 << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
} 