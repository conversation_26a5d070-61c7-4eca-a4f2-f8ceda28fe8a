#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX

using namespace std;

// Simplified move penalty - use integer for better performance
const int MOVE_PENALTY = 50;

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;

    void add_task(int start_time, int finish_time, int mem_needed) {
        // Inserts events while maintaining sort order by time.
        // lower_bound finds the first element not less than the given value.
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, { start_time, mem_needed });

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, { finish_time, -mem_needed });
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1; // Using -1 to represent infinity/failure
        }

        int current_mem_usage = 0; // Changed from long long to int
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            }
            else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        // If not enough memory, check future event points for released memory
        // Find the first event that happens after arrival_time
        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        for (auto i = it; i != events.end(); ++i) {
            current_mem_usage += i->second;
            if (current_mem_usage + mem_needed <= m) {
                return i->first; // Found a slot starting at this event's time
            }
        }
        return -1; // No suitable slot found
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    // (time, server_id, npu_id_in_server, batch_size)
    vector<tuple<int, int, int, int>> requests;

    // Calculate urgency: how much of the time window has passed
    double get_urgency(long long current_time) const {
        if (e <= s) return 1.0;
        return max(0.0, min(1.0, (double)(current_time - s) / (e - s)));
    }

    // Calculate sample density (samples per remaining time)
    double get_sample_density(long long current_time) const {
        long long remaining_time = max(1LL, (long long)e - current_time);
        return (double)samples_left / remaining_time;
    }

    // Calculate comprehensive priority (lower value = higher priority)
    long long get_priority(long long current_time) const {
        double urgency = get_urgency(current_time);
        double density = get_sample_density(current_time);

        // Priority = base_time - urgency_bonus - density_bonus
        // Higher urgency and density result in lower (better) priority value
        long long priority = next_send_time;
        priority -= (long long)(urgency * 10000); // Urgency bonus
        priority -= (long long)(density * 1000);  // Density bonus

        return priority;
    }
};


// --- Helper Functions ---

// Calculate correct inference time according to the problem statement
int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    // Use integer approximation to avoid expensive sqrt and ceil operations
    // ceil(sqrt(batch_size) / k) ≈ (int_sqrt(batch_size) + k - 1) / k
    int sqrt_batch = static_cast<int>(sqrt(batch_size) + 0.5); // Round to nearest integer
    return (sqrt_batch + k - 1) / k; // Integer ceiling division
}

// Enhanced batch size selection with mathematical optimization
int find_optimal_batch_size(int max_batch, int samples_left, int k, long long remaining_time, double urgency) {
    int limit = min(max_batch, (int)samples_left);

    // Generate smart candidates based on mathematical analysis
    vector<int> candidates;
    candidates.push_back(1);                    // Minimum
    candidates.push_back(limit);                // Maximum

    // Mathematical optimal: batch size around k^2 gives best efficiency for sqrt relationship
    int k_squared = k * k;
    if (k_squared <= limit) candidates.push_back(k_squared);
    if (k_squared / 2 <= limit) candidates.push_back(k_squared / 2);
    if (k_squared * 2 <= limit) candidates.push_back(min(k_squared * 2, limit));

    // Add power-of-2 candidates for efficiency
    for (int power = 1; power <= limit; power *= 2) {
        candidates.push_back(power);
    }

    // For urgent users, add smaller batch candidates for faster processing
    if (urgency > 0.7) {
        candidates.push_back(min(10, limit));
        candidates.push_back(min(25, limit));
        candidates.push_back(min(50, limit));
    }

    // Add candidates based on remaining time constraints
    for (int time_factor = 1; time_factor <= 5; time_factor++) {
        int time_based_batch = min(limit, (int)(remaining_time * k * time_factor / 10));
        if (time_based_batch > 0) candidates.push_back(time_based_batch);
    }

    // Remove duplicates and sort
    sort(candidates.begin(), candidates.end());
    candidates.erase(unique(candidates.begin(), candidates.end()), candidates.end());

    int best_batch = 1;
    double best_score = -1.0;

    for (int batch : candidates) {
        if (batch <= 0 || batch > limit) continue;

        int inference_time = calculate_inference_time(batch, k);
        if (inference_time > remaining_time) continue;

        // Enhanced scoring: efficiency + urgency consideration
        double efficiency = (double)batch / inference_time;
        double urgency_factor = urgency > 0.8 ? 1.5 : 1.0; // Bonus for urgent users
        double score = efficiency * urgency_factor;

        if (score > best_score) {
            best_score = score;
            best_batch = batch;
        }
    }

    return best_batch;
}

// Calculate dynamic move penalty based on user urgency
int get_dynamic_move_penalty(const User& user, long long current_time) {
    double urgency = user.get_urgency(current_time);

    // Reduce move penalty for urgent users
    if (urgency > 0.8) {
        return MOVE_PENALTY / 3; // Significantly reduce for very urgent
    } else if (urgency > 0.6) {
        return MOVE_PENALTY / 2; // Moderately reduce for urgent
    } else if (urgency < 0.3) {
        return MOVE_PENALTY * 2; // Increase penalty when there's plenty of time
    }

    return MOVE_PENALTY;
}

// --- Main Logic ---

int main() {
    // Fast I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    // 1. Read Input
    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    // 2. Initialize Models
    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({ npu_counter++, server.id, server.k, server.m, {} });
        }
    }

    // 3. Event-driven Scheduling with intelligent prioritization
    // Use comprehensive priority considering urgency and sample density
    priority_queue<pair<long long, int>> user_pq;
    for (const auto& user : users) {
        // Use intelligent priority calculation
        long long priority = user.get_priority(user.next_send_time);
        user_pq.push({-priority, user.id}); // Negative for min-heap behavior
    }

    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        long long time = -top.first;
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        long long send_time = max(time, user.next_send_time);

        // --- Decision Making with simplified but effective cost function ---
        long long best_cost = -1;
        int best_finish_time = -1;
        int best_npu_idx = -1;
        int best_batch_size = -1;

        // Calculate user's remaining time
        long long remaining_time = max(0LL, (long long)user.e - send_time);

        for (int i = 0; i < npus.size(); ++i) {
            const auto& npu = npus[i];
            const auto& server = servers_data[npu.server_id];

            if (server.m <= B) continue;
            int max_b_for_npu = (server.m - B) / A;
            if (max_b_for_npu <= 0) continue;

            // Use enhanced batch size selection
            double urgency = user.get_urgency(send_time);
            int good_batch = find_optimal_batch_size(max_b_for_npu, user.samples_left, server.k, remaining_time, urgency);
            int mem_needed = A * good_batch + B;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            // Use correct inference time calculation
            int inference_time = calculate_inference_time(good_batch, server.k);
            int finish_time = start_time + inference_time;

            // Enhanced cost function with dynamic penalties
            long long cost = finish_time;

            // Dynamic move penalty based on user urgency
            if (user.last_npu_id != -1 && npu.id != user.last_npu_id) {
                cost += get_dynamic_move_penalty(user, send_time);
            }

            // Progressive penalty for exceeding deadline (more severe for longer delays)
            if (finish_time > user.e) {
                long long overtime = finish_time - user.e;
                long long window_size = user.e - user.s;
                // Quadratic penalty: more severe for longer relative delays
                cost += overtime * overtime / max(1LL, window_size) * 10;
            }

            // Small bonus for high-efficiency NPUs to encourage load balancing
            cost -= server.k; // Higher k (speed) = lower cost

            if (best_cost == -1 || cost < best_cost) {
                best_cost = cost;
                best_finish_time = finish_time;
                best_npu_idx = i;
                best_batch_size = good_batch;
            }
        }

        // --- Commit to the best decision ---
        if (best_npu_idx != -1) {
            NPU& chosen_npu = npus[best_npu_idx];
            int batch = best_batch_size;

            int mem_needed = A * batch + B;
            int latency = latencies[chosen_npu.server_id][user.id];
            int server_k = servers_data[chosen_npu.server_id].k;
            int inference_time = calculate_inference_time(batch, server_k); // Use correct calculation
            int start_time = best_finish_time - inference_time;

            chosen_npu.add_task(start_time, best_finish_time, mem_needed);

            int npu_id_in_server;
            int base_npu_id = 0;
            for (int i = 0; i < chosen_npu.server_id; ++i) {
                base_npu_id += servers_data[i].g;
            }
            npu_id_in_server = chosen_npu.id - base_npu_id + 1;

            user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

            user.samples_left -= batch;
            user.next_send_time = send_time + latency + 1;
            user.last_npu_id = chosen_npu.id;

            if (user.samples_left > 0) {
                // Update priority with new context
                long long new_priority = user.get_priority(user.next_send_time);
                user_pq.push({-new_priority, user.id});
            }
        }
    }

    // 4. Print Output
    for (auto& user : users) {
        sort(user.requests.begin(), user.requests.end());
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
}