#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits>

using namespace std;

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
    
    Server(int id, int g, int k, int m) : id(id), g(g), k(k), m(m) {}
};

struct Request {
    int arrival_time;
    int user_id;
    int batch_size;
    int mem_needed;
    int request_id;
    
    Request(int arrival_time, int user_id, int batch_size, int mem_needed, int request_id) 
        : arrival_time(arrival_time), user_id(user_id), batch_size(batch_size), 
          mem_needed(mem_needed), request_id(request_id) {}
    
    bool operator<(const Request& other) const {
        if (arrival_time != other.arrival_time) {
            return arrival_time < other.arrival_time;
        }
        return user_id < other.user_id;
    }
};

struct RunningTask {
    int finish_time;
    int mem_needed;
    int user_id;
    int request_id;
    
    RunningTask(int finish_time, int mem_needed, int user_id, int request_id)
        : finish_time(finish_time), mem_needed(mem_needed), user_id(user_id), request_id(request_id) {}
};

class NPUQueue {
public:
    int npu_id;
    int server_id;
    int k; // 推理速度系数
    int m; // 显存大小
    vector<Request> queue; // 等待处理的请求
    vector<RunningTask> running_tasks; // 正在运行的任务
    int current_memory;
    
    NPUQueue(int npu_id, int server_id, int k, int m) 
        : npu_id(npu_id), server_id(server_id), k(k), m(m), current_memory(0) {}
    
    void add_request(int arrival_time, int user_id, int batch_size, int mem_needed, int request_id) {
        queue.emplace_back(arrival_time, user_id, batch_size, mem_needed, request_id);
        sort(queue.begin(), queue.end());
    }
    
    vector<tuple<int, int, int>> process_time_step(int current_time) {
        vector<tuple<int, int, int>> completed_this_step;
        
        // 1. 移除已完成推理的请求
        auto new_end = remove_if(running_tasks.begin(), running_tasks.end(),
            [&](const RunningTask& task) {
                if (task.finish_time <= current_time) {
                    completed_this_step.emplace_back(task.user_id, task.request_id, current_time);
                    current_memory -= task.mem_needed;
                    return true;
                }
                return false;
            });
        running_tasks.erase(new_end, running_tasks.end());
        
        // 2. 处理队列中的请求
        vector<int> processed_indices;
        for (int i = 0; i < queue.size(); ++i) {
            const auto& req = queue[i];
            if (req.arrival_time <= current_time) {
                if (current_memory + req.mem_needed <= m) {
                    // 分配推理资源
                    int inference_time = static_cast<int>(ceil(static_cast<double>(req.batch_size) / k));
                    int finish_time = current_time + inference_time;
                    running_tasks.emplace_back(finish_time, req.mem_needed, req.user_id, req.request_id);
                    current_memory += req.mem_needed;
                    processed_indices.push_back(i);
                }
            }
        }
        
        // 移除已处理的请求
        for (int i = processed_indices.size() - 1; i >= 0; --i) {
            queue.erase(queue.begin() + processed_indices[i]);
        }
        
        return completed_this_step;
    }
    
    bool can_accept_request(int mem_needed) const {
        return current_memory + mem_needed <= m;
    }
    
    int get_load() const {
        return queue.size() + running_tasks.size();
    }
};

struct User {
    int id;
    int s, e, cnt;
    int samples_left;
    int next_send_time;
    int last_npu_global_id;
    vector<tuple<int, int, int, int>> requests; // (send_time, server_id, npu_id, batch_size)
    
    User(int id, int s, int e, int cnt) 
        : id(id), s(s), e(e), cnt(cnt), samples_left(cnt), next_send_time(s), last_npu_global_id(-1) {}
    
    bool can_send_at_time(int time) const {
        return time >= next_send_time && time < e && samples_left > 0;
    }
};

// --- Helper Functions ---

int get_npu_global_id(const NPUQueue& npu, const vector<Server>& servers) {
    int base_id = 0;
    for (int i = 0; i < npu.server_id; ++i) {
        base_id += servers[i].g;
    }
    return base_id + npu.npu_id;
}

vector<int> get_batch_size_options(int samples_left, int max_batch_memory) {
    vector<int> options;
    int max_batch = min(samples_left, max_batch_memory);
    
    if (max_batch <= 0) {
        return options;
    }
    
    // 如果能一次完成所有样本，优先选择
    if (max_batch >= samples_left) {
        options.push_back(samples_left);
        return options;
    }
    
    // 否则考虑多种批量大小
    options.push_back(max_batch);
    
    // 添加一些中等大小的选项
    if (max_batch > 100) {
        options.push_back(max_batch / 2);
    }
    if (max_batch > 200) {
        options.push_back(max_batch / 4);
    }
    
    // 添加小批量选项（快速处理）
    if (max_batch > 50) {
        options.push_back(min(50, max_batch));
    }
    
    // 按从大到小排序
    sort(options.rbegin(), options.rend());
    // 去重
    options.erase(unique(options.begin(), options.end()), options.end());
    
    return options;
}

long long calculate_choice_cost(const User& user, const NPUQueue& npu, const Server& server, 
                               int batch_size, int current_time, int latency, 
                               const vector<Server>& servers) {
    // 基础成本：完成时间
    int inference_time = static_cast<int>(ceil(static_cast<double>(batch_size) / server.k));
    int arrival_time = current_time + latency;
    int estimated_start_time = arrival_time; // 简化估算
    int finish_time = estimated_start_time + inference_time;
    
    long long cost = finish_time;
    
    // 截止时间惩罚
    if (finish_time > user.e) {
        cost += 10000; // 严重惩罚超时
    }
    
    // 迁移惩罚
    int current_npu_global_id = get_npu_global_id(npu, servers);
    if (user.last_npu_global_id != -1 && user.last_npu_global_id != current_npu_global_id) {
        cost += 2000; // 大幅提高迁移成本
    }
    
    // NPU负载惩罚
    cost += npu.get_load() * 10;
    
    // 批量大小奖励（较大的batch减少总请求数）
    if (batch_size >= user.samples_left) {
        cost -= 500; // 能够完成所有剩余样本的奖励
    }
    
    return cost;
}

tuple<int, int, int> find_best_npu_choice(const User& user, int current_time, 
                                        vector<NPUQueue>& npus, const vector<Server>& servers,
                                        const vector<vector<int>>& latencies, int A, int B) {
    int best_npu_idx = -1;
    int best_batch_size = -1;
    long long best_cost = LLONG_MAX;
    
    for (int i = 0; i < npus.size(); ++i) {
        const auto& npu = npus[i];
        const auto& server = servers[npu.server_id];
        int latency = latencies[server.id][user.id];
        
        // 计算可能的批量大小
        int max_batch_memory = (server.m - B) / A;
        if (max_batch_memory <= 0) {
            continue;
        }
        
        // 尝试不同的批量大小
        vector<int> batch_options = get_batch_size_options(user.samples_left, max_batch_memory);
        
        for (int batch_size : batch_options) {
            int mem_needed = A * batch_size + B;
            
            if (!npu.can_accept_request(mem_needed)) {
                continue;
            }
            
            // 计算成本
            long long cost = calculate_choice_cost(user, npu, server, batch_size, 
                                                 current_time, latency, servers);
            
            if (cost < best_cost) {
                best_cost = cost;
                best_npu_idx = i;
                best_batch_size = batch_size;
            }
        }
    }
    
    return make_tuple(best_npu_idx, best_batch_size, current_time);
}

vector<User> simulate_scheduling(const vector<tuple<int, int, int>>& servers_data,
                               const vector<tuple<int, int, int>>& users_data,
                               const vector<vector<int>>& latencies, int A, int B) {
    int N = servers_data.size();
    int M = users_data.size();
    
    // 初始化服务器和用户
    vector<Server> servers;
    for (int i = 0; i < N; ++i) {
        servers.emplace_back(i, get<0>(servers_data[i]), get<1>(servers_data[i]), get<2>(servers_data[i]));
    }
    
    vector<User> users;
    for (int i = 0; i < M; ++i) {
        users.emplace_back(i, get<0>(users_data[i]), get<1>(users_data[i]), get<2>(users_data[i]));
    }
    
    // 初始化NPU队列
    vector<NPUQueue> npus;
    for (const auto& server : servers) {
        for (int npu_local_id = 0; npu_local_id < server.g; ++npu_local_id) {
            npus.emplace_back(npu_local_id, server.id, server.k, server.m);
        }
    }
    
    // 事件驱动调度
    int request_id_counter = 0;
    int current_time = 0;
    int max_time = 0;
    for (const auto& user : users) {
        max_time = max(max_time, user.e);
    }
    max_time += 1000; // 添加缓冲时间
    
    // 用户发送请求的事件队列
    priority_queue<pair<int, int>, vector<pair<int, int>>, greater<pair<int, int>>> user_events;
    for (const auto& user : users) {
        user_events.push({user.s, user.id});
    }
    
    while (current_time <= max_time && !user_events.empty()) {
        // 处理所有NPU的当前时间步
        for (auto& npu : npus) {
            npu.process_time_step(current_time);
        }
        
        // 处理用户发送请求事件
        while (!user_events.empty() && user_events.top().first <= current_time) {
            int user_id = user_events.top().second;
            user_events.pop();
            User& user = users[user_id];
            
            if (!user.can_send_at_time(current_time)) {
                continue;
            }
            
            // 为用户选择最佳的NPU和批量大小
            auto [best_npu_idx, best_batch_size, send_time] = 
                find_best_npu_choice(user, current_time, npus, servers, latencies, A, B);
            
            if (best_npu_idx != -1) {
                NPUQueue& npu = npus[best_npu_idx];
                const Server& server = servers[npu.server_id];
                int latency = latencies[server.id][user.id];
                int arrival_time = send_time + latency;
                int mem_needed = A * best_batch_size + B;
                
                // 发送请求到选定的NPU
                npu.add_request(arrival_time, user.id, best_batch_size, mem_needed, request_id_counter);
                request_id_counter++;
                
                // 记录用户的请求
                int npu_local_id = npu.npu_id + 1; // 转换为1索引
                user.requests.emplace_back(send_time, server.id + 1, npu_local_id, best_batch_size);
                
                // 更新用户状态
                user.samples_left -= best_batch_size;
                user.next_send_time = send_time + latency + 1;
                user.last_npu_global_id = get_npu_global_id(npu, servers);
                
                // 如果还有样本需要处理，添加下一个发送事件
                if (user.samples_left > 0 && user.next_send_time < user.e) {
                    user_events.push({user.next_send_time, user.id});
                }
            }
        }
        
        current_time++;
    }
    
    return users;
}

int main() {
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);
    
    try {
        // 读取输入
        int N;
        cin >> N;
        
        vector<tuple<int, int, int>> servers_data;
        for (int i = 0; i < N; ++i) {
            int g, k, m;
            cin >> g >> k >> m;
            servers_data.emplace_back(g, k, m);
        }
        
        int M;
        cin >> M;
        
        vector<tuple<int, int, int>> users_data;
        for (int i = 0; i < M; ++i) {
            int s, e, cnt;
            cin >> s >> e >> cnt;
            users_data.emplace_back(s, e, cnt);
        }
        
        vector<vector<int>> latencies(N, vector<int>(M));
        for (int i = 0; i < N; ++i) {
            for (int j = 0; j < M; ++j) {
                cin >> latencies[i][j];
            }
        }
        
        int A, B;
        cin >> A >> B;
        
        // 运行调度模拟
        vector<User> users = simulate_scheduling(servers_data, users_data, latencies, A, B);
        
        // 输出结果
        for (auto& user : users) {
            if (!user.requests.empty()) {
                sort(user.requests.begin(), user.requests.end());
            }
            cout << user.requests.size() << "\n";
            
            if (!user.requests.empty()) {
                bool first = true;
                for (const auto& req : user.requests) {
                    if (!first) cout << " ";
                    cout << get<0>(req) << " " << get<1>(req) << " " 
                         << get<2>(req) << " " << get<3>(req);
                    first = false;
                }
            }
            cout << "\n";
        }
        
    } catch (const exception& e) {
        cerr << "Error: " << e.what() << endl;
        return 1;
    }
    
    return 0;
} 