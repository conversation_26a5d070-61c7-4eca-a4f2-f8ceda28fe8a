#include <iostream>
#include <fstream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <unordered_map>
#include <iomanip>
#include <climits>

using namespace std;

const double INF = 1e18;
const int MAX_USERS = 505;
const int MAX_SERVERS = 15;
const int MAX_NPU_PER_SERVER = 15;

// 服务器类型配置
struct ServerType {
    int g;       // NPU数量
    int k;       // 推理速度系数
    int m;       // 显存大小(MB)
    int B_max;   // 最大batchsize
};

// 用户请求配置
struct User {
    int id;
    int s, e;   // 时间窗口 [s, e]
    int cnt;     // 样本数量
    int latency[MAX_SERVERS]; // 到每种服务器的延迟
    vector<int> send_times; // 发送时间序列
    vector<int> batches;    // 批次大小序列
    vector<int> server_types; // 服务器类型序列
    vector<int> npu_indices;  // NPU索引序列
    int assigned_server_type; // 分配的服务器类型
    int assigned_npu;         // 分配的NPU索引
    int last_finish;          // 最后一个样本完成时间
    int move_count;           // 迁移次数
};

// 请求事件
struct Request {
    int user_id;
    int batch;
    int server_type;
    int npu_index;
    int send_time;
    int arrive_time;
    int start_time;
    int finish_time;
    int mem_used;          // 显存占用
};

// NPU状态
struct NPUState {
    int server_type;          // 所属服务器类型
    int npu_index;            // NPU索引
    int free_mem;             // 剩余显存
    double current_load;      // 当前负载(已分配样本数)
    queue<Request*> waiting_queue; // 等待队列
};

// 事件类型
enum EventType { FINISH, ARRIVE, SEND }; // 0,1,2 优先级顺序

// 事件结构
struct Event {
    int time;
    EventType type;
    Request* req;
    NPUState* npu;
    
    // 优先级队列比较函数
    bool operator>(const Event& other) const {
        if (time != other.time) {
            return time > other.time;
        }
        return type > other.type; // 类型小的优先级高
    }
};

// 全局变量
vector<ServerType> server_types;
vector<User> users;
vector<NPUState> npus;
int a, b, N, M;

// 计算处理时间（毫秒，向上取整）
int compute_process_time(int batch, int k) {
    double raw_time = batch * 1.0 / (k * sqrt(batch));
    return static_cast<int>(ceil(raw_time));
}

// 计算显存占用
int compute_memory(int batch) {
    return a * batch + b;
}

// 初始化NPU列表
void initialize_npus() {
    npus.clear();
    for (int st = 0; st < N; st++) {
        for (int i = 0; i < server_types[st].g; i++) {
            NPUState npu;
            npu.server_type = st;
            npu.npu_index = i;
            npu.free_mem = server_types[st].m;
            npu.current_load = 0;
            npus.push_back(npu);
        }
    }
}

// 分配用户到NPU
void assign_users_to_npus() {
    // 按处理能力排序NPU (能力 = k * sqrt(B_max))
    sort(npus.begin(), npus.end(), [](const NPUState& a, const NPUState& b) {
        double cap_a = server_types[a.server_type].k * 
                      sqrt(server_types[a.server_type].B_max);
        double cap_b = server_types[b.server_type].k * 
                      sqrt(server_types[b.server_type].B_max);
        return cap_a > cap_b;
    });

    // 用户按样本量降序排序
    vector<User*> sorted_users;
    for (int i = 0; i < M; i++) {
        sorted_users.push_back(&users[i]);
    }
    sort(sorted_users.begin(), sorted_users.end(), [](User* a, User* b) {
        return a->cnt > b->cnt;
    });

    // 贪心分配
    for (User* user : sorted_users) {
        // 选择延迟最小的服务器类型
        int best_st = 0;
        for (int st = 1; st < N; st++) {
            if (user->latency[st] < user->latency[best_st]) {
                best_st = st;
            }
        }
        user->assigned_server_type = best_st;

        // 选择负载最轻的NPU
        int best_npu_idx = -1;
        double min_load = INF;
        for (int i = 0; i < npus.size(); i++) {
            if (npus[i].server_type == best_st && npus[i].current_load < min_load) {
                min_load = npus[i].current_load;
                best_npu_idx = i;
            }
        }

        if (best_npu_idx != -1) {
            user->assigned_npu = best_npu_idx;
            npus[best_npu_idx].current_load += user->cnt;
        }
    }
}

// 改进的调度计划生成（严格通信时延约束）
void generate_initial_schedule() {
    for (User& user : users) {
        int st = user.assigned_server_type;
        int npu_idx = user.assigned_npu;
        int B_max = min(server_types[st].B_max, 1000);
        int latency = user.latency[st];
        int T_i = (user.cnt + B_max - 1) / B_max; // 向上取整
        
        user.send_times.clear();
        user.batches.clear();
        user.server_types.clear();
        user.npu_indices.clear();
        
        // 严格的通信时延约束处理
        int current_send_time = user.s;
        int last_arrival_time = -1;
        
        // 生成发送时间序列
        for (int i = 0; i < T_i; i++) {
            // 确保满足通信约束：只有在上个请求到达后才能发送下个请求
            if (last_arrival_time != -1) {
                current_send_time = max(current_send_time, last_arrival_time + 1);
            }
            
            user.send_times.push_back(current_send_time);
            user.server_types.push_back(st);
            user.npu_indices.push_back(npus[user.assigned_npu].npu_index);
            
            // 更新到达时间
            last_arrival_time = current_send_time + latency;
            
            // 为下次发送预留时间
            current_send_time = last_arrival_time + 1;
        }
        
        // 设置批次大小
        int remaining = user.cnt;
        for (int i = 0; i < T_i - 1; i++) {
            int batch = min(B_max, remaining);
            user.batches.push_back(batch);
            remaining -= batch;
        }
        user.batches.push_back(remaining);
    }
}

// 简化的事件驱动模拟用于评估
void evaluate_schedule() {
    // 重置用户完成时间
    for (User& user : users) {
        user.last_finish = 0;
        
        // 简单估算：最后一个请求的发送时间 + 通信延迟 + 处理时间
        if (!user.send_times.empty()) {
            int last_idx = user.send_times.size() - 1;
            int send_time = user.send_times[last_idx];
            int latency = user.latency[user.assigned_server_type];
            int arrive_time = send_time + latency;
            int process_time = compute_process_time(user.batches[last_idx], 
                                                  server_types[user.assigned_server_type].k);
            user.last_finish = arrive_time + process_time;
        }
    }
}

// 输出结果 
void output_solution() {
    for (int i = 0; i < M; i++) {
        User& user = users[i];
        cout << user.send_times.size() << endl;
        
        for (int j = 0; j < user.send_times.size(); j++) {
            cout << user.send_times[j] << " "
                 << user.server_types[j] + 1 << " "
                 << user.npu_indices[j] + 1 << " "
                 << user.batches[j];
            
            if (j < user.send_times.size() - 1) {
                cout << " ";
            }
        }
        cout << endl;
    }
}

// 主函数
int main() {
    
    cin >> N;
    server_types.resize(N);
    for (int i = 0; i < N; i++) {
        cin >> server_types[i].g >> server_types[i].k >> server_types[i].m;
    }
    
    cin >> M;
    users.resize(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].id = i;
        users[i].move_count = 0;
    }
    
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> users[j].latency[i];
        }
    }
    
    cin >> a >> b;
    
    
    // 计算最大batchsize
    for (int i = 0; i < N; i++) {
        server_types[i].B_max = min(1000, (server_types[i].m - b) / a);
    }
    
    initialize_npus();
    assign_users_to_npus();
    generate_initial_schedule();
    
    // 评估调度效果
    evaluate_schedule();
    
    output_solution();
  
    return 0;
} 