#include <iostream>
#include <fstream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <unordered_map>
#include <iomanip>
#include <climits>
#include <set>

using namespace std;

const double INF = 1e18;
const int MAX_USERS = 505;
const int MAX_SERVERS = 15;
const int MAX_NPU_PER_SERVER = 15;

// 服务器类型配置
struct ServerType {
    int g;       // NPU数量
    int k;       // 推理速度系数
    int m;       // 显存大小(MB)
    int B_max;   // 最大batchsize
    double capacity; // 处理能力评估
};

// 用户请求配置
struct User {
    int id;
    int s, e;   // 时间窗口 [s, e]
    int cnt;     // 样本数量
    int latency[MAX_SERVERS]; // 到每种服务器的延迟
    vector<int> send_times; // 发送时间序列
    vector<int> batches;    // 批次大小序列
    vector<int> server_types; // 服务器类型序列
    vector<int> npu_indices;  // NPU索引序列
    int assigned_server_type; // 分配的服务器类型
    int assigned_npu;         // 分配的NPU索引
    int last_finish;          // 最后一个样本完成时间
    int move_count;           // 迁移次数
    double urgency;           // 紧急度评估
};

// 请求事件
struct Request {
    int user_id;
    int batch;
    int server_type;
    int npu_index;
    int send_time;
    int arrive_time;
    int start_time;
    int finish_time;
    int mem_used;          // 显存占用
    
    // 用于排序的比较函数
    bool operator<(const Request& other) const {
        if (arrive_time != other.arrive_time) {
            return arrive_time < other.arrive_time;
        }
        return user_id < other.user_id;
    }
};

// NPU状态
struct NPUState {
    int server_type;          // 所属服务器类型
    int npu_index;            // NPU索引
    int free_mem;             // 剩余显存
    double current_load;      // 当前负载(已分配样本数)
    vector<Request*> waiting_queue; // 等待队列，改用vector以便排序
    vector<Request*> processing;    // 正在处理的请求
};

// 事件类型
enum EventType { FINISH, ARRIVE, SEND }; // 0,1,2 优先级顺序

// 事件结构
struct Event {
    int time;
    EventType type;
    Request* req;
    NPUState* npu;
    
    // 优先级队列比较函数
    bool operator>(const Event& other) const {
        if (time != other.time) {
            return time > other.time;
        }
        return type > other.type; // 类型小的优先级高
    }
};

// 全局变量
vector<ServerType> server_types;
vector<User> users;
vector<NPUState> npus;
int a, b, N, M;
double total_score = 0;

// 函数声明
void generate_optimized_schedule_for_user(User& user);

// 计算处理时间（毫秒，向上取整）
int compute_process_time(int batch, int k) {
    double raw_time = batch * 1.0 / (k * sqrt(batch));
    return static_cast<int>(ceil(raw_time));
}

// 计算显存占用
int compute_memory(int batch) {
    return a * batch + b;
}

// 计算用户紧急度
double calculate_user_urgency(const User& user) {
    double time_pressure = (double)user.cnt * 5.0 / (user.e - user.s);
    return time_pressure;
}

// 计算NPU综合评分（越小越好）
double calculate_npu_score(const User& user, int npu_idx) {
    const NPUState& npu = npus[npu_idx];
    const ServerType& st = server_types[npu.server_type];
    
    // 延迟权重
    double latency_score = user.latency[npu.server_type] * 2.0;
    
    // 负载权重
    double load_score = npu.current_load * 0.1;
    
    // 处理能力权重（负值表示能力越强越好）
    double capacity_score = -st.capacity * 0.5;
    
    // 显存充足度权重
    int max_batch = min(1000, (st.m - b) / a);
    double mem_score = max_batch > 0 ? -max_batch * 0.01 : 1000;
    
    return latency_score + load_score + capacity_score + mem_score;
}

// 计算最优批次大小
int calculate_optimal_batch(const User& user, int server_type) {
    const ServerType& st = server_types[server_type];
    int max_batch = min(1000, (st.m - b) / a);
    
    if (max_batch <= 0) return 1;
    
    // 基于时间窗口和处理能力的动态调整
    int time_window = user.e - user.s;
    int latency = user.latency[server_type];
    
    // 如果时间窗口紧张，适当减小批次以减少单次处理时间
    if (time_window < user.cnt * 20) {
        max_batch = min(max_batch, 50);
    }
    
    // 根据处理能力调整
    if (st.k >= 4) {
        max_batch = min(max_batch, 200); // 高性能服务器可以用较大批次
    } else if (st.k <= 2) {
        max_batch = min(max_batch, 80);  // 低性能服务器用较小批次
    }
    
    return max(1, max_batch);
}

// 初始化NPU列表
void initialize_npus() {
    npus.clear();
    for (int st = 0; st < N; st++) {
        // 计算服务器处理能力
        server_types[st].capacity = server_types[st].k * sqrt(min(1000, (server_types[st].m - b) / a));
        
        for (int i = 0; i < server_types[st].g; i++) {
            NPUState npu;
            npu.server_type = st;
            npu.npu_index = i;
            npu.free_mem = server_types[st].m;
            npu.current_load = 0;
            npus.push_back(npu);
        }
    }
}

// 智能分配用户到NPU
void assign_users_to_npus_improved() {
    // 计算用户紧急度
    for (User& user : users) {
        user.urgency = calculate_user_urgency(user);
    }
    
    // 按紧急度降序排序用户
    vector<User*> sorted_users;
    for (int i = 0; i < M; i++) {
        sorted_users.push_back(&users[i]);
    }
    sort(sorted_users.begin(), sorted_users.end(), [](User* a, User* b) {
        return a->urgency > b->urgency;
    });

    // 为每个用户分配最优NPU
    for (User* user : sorted_users) {
        int best_npu_idx = -1;
        double best_score = INF;
        
        // 遍历所有NPU，找到综合评分最优的
        for (int i = 0; i < npus.size(); i++) {
            double score = calculate_npu_score(*user, i);
            if (score < best_score) {
                best_score = score;
                best_npu_idx = i;
            }
        }

        if (best_npu_idx != -1) {
            user->assigned_npu = best_npu_idx;
            user->assigned_server_type = npus[best_npu_idx].server_type;
            npus[best_npu_idx].current_load += user->cnt;
        }
    }
}

// 生成优化的调度计划
void generate_optimized_schedule() {
    for (User& user : users) {
        int st = user.assigned_server_type;
        int npu_idx = user.assigned_npu;
        int optimal_batch = calculate_optimal_batch(user, st);
        int latency = user.latency[st];
        
        user.send_times.clear();
        user.batches.clear();
        user.server_types.clear();
        user.npu_indices.clear();
        
        // 计算需要的批次数
        int T_i = (user.cnt + optimal_batch - 1) / optimal_batch;
        
        // 动态调整发送间隔，更紧凑的安排
        int base_interval = latency;
        int time_window = user.e - user.s;
        int required_time = T_i * (base_interval + 10); // 估算需要的总时间
        
        if (required_time > time_window) {
            // 时间紧张，尝试更紧凑的安排
            base_interval = max(latency, (time_window - T_i * 10) / T_i);
        }
        
        // 生成发送时间序列
        int start_time = max(user.s, 0);
        for (int i = 0; i < T_i; i++) {
            int send_time = start_time + i * base_interval;
            user.send_times.push_back(send_time);
            user.server_types.push_back(st);
            user.npu_indices.push_back(npus[user.assigned_npu].npu_index);
        }
        
        // 设置批次大小
        int remaining = user.cnt;
        for (int i = 0; i < T_i - 1; i++) {
            int batch = min(optimal_batch, remaining);
            user.batches.push_back(batch);
            remaining -= batch;
        }
        user.batches.push_back(remaining);
    }
}

// 改进的事件驱动模拟
void improved_simulation() {
    // 收集所有事件时间点
    set<int> time_points;
    vector<Request*> all_requests;
    
    // 创建所有请求
    for (User& user : users) {
        for (int i = 0; i < user.send_times.size(); i++) {
            Request* req = new Request();
            req->user_id = user.id;
            req->batch = user.batches[i];
            req->server_type = user.server_types[i];
            req->npu_index = user.npu_indices[i];
            req->send_time = user.send_times[i];
            req->arrive_time = req->send_time + user.latency[req->server_type];
            req->mem_used = compute_memory(req->batch);
            
            all_requests.push_back(req);
            time_points.insert(req->arrive_time);
        }
    }
    
    // 重置NPU状态
    for (NPUState& npu : npus) {
        npu.free_mem = server_types[npu.server_type].m;
        npu.waiting_queue.clear();
        npu.processing.clear();
    }
    
    // 重置用户完成时间
    for (User& user : users) {
        user.last_finish = -1;
    }
    
    // 时间驱动模拟
    for (int current_time : time_points) {
        // 1. 移除已完成推理的请求
        for (NPUState& npu : npus) {
            auto it = npu.processing.begin();
            while (it != npu.processing.end()) {
                if ((*it)->finish_time <= current_time) {
                    // 释放资源
                    npu.free_mem += (*it)->mem_used;
                    // 更新用户完成时间
                    User& user = users[(*it)->user_id];
                    user.last_finish = max(user.last_finish, (*it)->finish_time);
                    it = npu.processing.erase(it);
                } else {
                    ++it;
                }
            }
        }
        
        // 2. 增加当前时刻接收到的请求
        for (Request* req : all_requests) {
            if (req->arrive_time == current_time) {
                // 找到对应的NPU
                for (NPUState& npu : npus) {
                    if (npu.server_type == req->server_type && 
                        npu.npu_index == req->npu_index) {
                        npu.waiting_queue.push_back(req);
                        break;
                    }
                }
            }
        }
        
        // 3. 对每个NPU的队列排序并分配资源
        for (NPUState& npu : npus) {
            if (npu.waiting_queue.empty()) continue;
            
            // 按到达时间和用户编号排序
            sort(npu.waiting_queue.begin(), npu.waiting_queue.end(), 
                 [](Request* a, Request* b) { return *a < *b; });
            
            // 4. 从队首至队尾依次扫描请求
            auto it = npu.waiting_queue.begin();
            while (it != npu.waiting_queue.end()) {
                if (npu.free_mem >= (*it)->mem_used) {
                    // 分配资源
                    Request* req = *it;
                    req->start_time = current_time;
                    int k = server_types[req->server_type].k;
                    req->finish_time = req->start_time + compute_process_time(req->batch, k);
                    
                    npu.free_mem -= req->mem_used;
                    npu.processing.push_back(req);
                    
                    it = npu.waiting_queue.erase(it);
                } else {
                    ++it;
                }
            }
        }
    }
    
    // 清理请求内存
    for (Request* req : all_requests) {
        delete req;
    }
}

// 优化超时用户
void optimize_overtime_users() {
    vector<User*> overtime_users;
    for (User& user : users) {
        if (user.last_finish > user.e) {
            overtime_users.push_back(&user);
        }
    }
    
    if (overtime_users.empty()) return;
    
    // 按超时程度排序
    sort(overtime_users.begin(), overtime_users.end(), [](User* a, User* b) {
        return (a->last_finish - a->e) > (b->last_finish - b->e);
    });
    
    for (User* user : overtime_users) {
        bool improved = false;
        
        // 策略1：减少批次大小
        if (!improved) {
            bool can_reduce = false;
            for (int& batch : user->batches) {
                int new_batch = max(1, (int)(batch * 0.8));
                if (new_batch != batch) {
                    batch = new_batch;
                    can_reduce = true;
                }
            }
            if (can_reduce) {
                improved = true;
            }
        }
        
        // 策略2：迁移到更好的NPU
        if (!improved) {
            int current_npu = user->assigned_npu;
            npus[current_npu].current_load -= user->cnt;
            
            int best_npu_idx = -1;
            double best_score = INF;
            
            for (int i = 0; i < npus.size(); i++) {
                double score = calculate_npu_score(*user, i);
                if (score < best_score) {
                    best_score = score;
                    best_npu_idx = i;
                }
            }
            
            if (best_npu_idx != -1 && best_npu_idx != current_npu) {
                user->assigned_npu = best_npu_idx;
                user->assigned_server_type = npus[best_npu_idx].server_type;
                npus[best_npu_idx].current_load += user->cnt;
                user->move_count++;
                
                // 重新生成调度计划
                generate_optimized_schedule_for_user(*user);
                improved = true;
            } else {
                npus[current_npu].current_load += user->cnt; // 恢复原状态
            }
        }
    }
}

// 为单个用户重新生成调度计划
void generate_optimized_schedule_for_user(User& user) {
    int st = user.assigned_server_type;
    int optimal_batch = calculate_optimal_batch(user, st);
    int latency = user.latency[st];
    
    user.send_times.clear();
    user.batches.clear();
    user.server_types.clear();
    user.npu_indices.clear();
    
    int T_i = (user.cnt + optimal_batch - 1) / optimal_batch;
    int base_interval = max(latency, (user.e - user.s - T_i * 10) / T_i);
    
    int start_time = max(user.s, 0);
    for (int i = 0; i < T_i; i++) {
        user.send_times.push_back(start_time + i * base_interval);
        user.server_types.push_back(st);
        user.npu_indices.push_back(npus[user.assigned_npu].npu_index);
    }
    
    int remaining = user.cnt;
    for (int i = 0; i < T_i - 1; i++) {
        int batch = min(optimal_batch, remaining);
        user.batches.push_back(batch);
        remaining -= batch;
    }
    user.batches.push_back(remaining);
}

// 输出结果 
void output_solution() {
    for (int i = 0; i < M; i++) {
        User& user = users[i];
        cout << user.send_times.size() << endl;
        
        for (int j = 0; j < user.send_times.size(); j++) {
            cout << user.send_times[j] << " "
                 << user.server_types[j] + 1 << " "
                 << user.npu_indices[j] + 1 << " "
                 << user.batches[j];
            
            if (j < user.send_times.size() - 1) {
                cout << " ";
            }
        }
        cout << endl;
    }
}

// 主函数
int main() {
    cin >> N;
    server_types.resize(N);
    for (int i = 0; i < N; i++) {
        cin >> server_types[i].g >> server_types[i].k >> server_types[i].m;
    }
    
    cin >> M;
    users.resize(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].id = i;
        users[i].move_count = 0;
    }
    
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> users[j].latency[i];
        }
    }
    
    cin >> a >> b;
    
    // 计算最大batchsize
    for (int i = 0; i < N; i++) {
        server_types[i].B_max = min(1000, (server_types[i].m - b) / a);
    }
    
    initialize_npus();
    
    // 多轮迭代优化
    for (int iter = 0; iter < 3; iter++) {
        assign_users_to_npus_improved();
        generate_optimized_schedule();
        improved_simulation();
        
        // 检查是否所有用户都按时完成
        bool all_on_time = true;
        for (User& user : users) {
            if (user.last_finish > user.e) {
                all_on_time = false;
                break;
            }
        }
        
        if (all_on_time) break;
        
        optimize_overtime_users();
    }
    
    output_solution();
    
    return 0;
}