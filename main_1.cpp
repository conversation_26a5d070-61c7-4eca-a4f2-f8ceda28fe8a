#include <iostream>
#include <fstream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <unordered_map>
#include <iomanip>
#include <climits>

using namespace std;

const double INF = 1e18;
const int MAX_USERS = 505;
const int MAX_SERVERS = 15;
const int MAX_NPU_PER_SERVER = 15;

// 服务器类型配置
struct ServerType {
    int g;       // NPU数量
    int k;       // 推理速度系数
    int m;       // 显存大小(MB)
    int B_max;   // 最大batchsize
};

// 用户请求配置
struct User {
    int id;
    int s, e;   // 时间窗口 [s, e]
    int cnt;     // 样本数量
    int latency[MAX_SERVERS]; // 到每种服务器的延迟
    vector<int> send_times; // 发送时间序列
    vector<int> batches;    // 批次大小序列
    vector<int> server_types; // 服务器类型序列
    vector<int> npu_indices;  // NPU索引序列
    int assigned_server_type; // 分配的服务器类型
    int assigned_npu;         // 分配的NPU索引
    int last_finish;          // 最后一个样本完成时间
    int move_count;           // 迁移次数
};

// 请求事件
struct Request {
    int user_id;
    int batch;
    int server_type;
    int npu_index;
    int send_time;
    int arrive_time;
    int start_time;
    int finish_time;
    int mem_used;          // 显存占用
};

// NPU状态
struct NPUState {
    int server_type;          // 所属服务器类型
    int npu_index;            // NPU索引
    int free_mem;             // 剩余显存
    double current_load;      // 当前负载(已分配样本数)
    queue<Request*> waiting_queue; // 等待队列
};

// 事件类型
enum EventType { FINISH, ARRIVE, SEND }; // 0,1,2 优先级顺序

// 事件结构
struct Event {
    int time;
    EventType type;
    Request* req;
    NPUState* npu;
    
    // 优先级队列比较函数
    bool operator>(const Event& other) const {
        if (time != other.time) {
            return time > other.time;
        }
        return type > other.type; // 类型小的优先级高
    }
};

// 全局变量
vector<ServerType> server_types;
vector<User> users;
vector<NPUState> npus;
int a, b, N, M;
double total_score = 0;

// 计算处理时间（毫秒，向上取整）
int compute_process_time(int batch, int k) {
    double raw_time = batch * 1.0 / (k * sqrt(batch));
    return static_cast<int>(ceil(raw_time));
}

// 计算显存占用
int compute_memory(int batch) {
    return a * batch + b;
}

// 初始化NPU列表
void initialize_npus() {
    npus.clear();
    for (int st = 0; st < N; st++) {
        for (int i = 0; i < server_types[st].g; i++) {
            NPUState npu;
            npu.server_type = st;
            npu.npu_index = i;
            npu.free_mem = server_types[st].m;
            npu.current_load = 0;
            npus.push_back(npu);
        }
    }
}

// 计算最优批次大小
int compute_optimal_batch_size(int server_type, int max_samples) {
    int k = server_types[server_type].k;
    int memory_limit = server_types[server_type].m;
    
    // 确保有有效的批次大小范围
    if (memory_limit < b + a) {
        return 1; // 最小批次大小
    }
    
    int max_batch_by_memory = min(1000, (memory_limit - b) / a);
    int max_batch = min(max_batch_by_memory, max_samples);
    
    if (max_batch <= 0) {
        return 1;
    }
    
    // 找到使处理时间最小的批次大小
    int best_batch = 1;
    double min_time_per_sample = INF;
    
    for (int batch = 1; batch <= max_batch; batch++) {
        if (k > 0 && batch > 0) {
            double process_time = ceil(batch * 1.0 / (k * sqrt(batch)));
            double time_per_sample = process_time / batch;
            if (time_per_sample < min_time_per_sample) {
                min_time_per_sample = time_per_sample;
                best_batch = batch;
            }
        }
    }
    
    return best_batch;
}

// 计算服务器类型适配度分数
double compute_server_fitness(int user_id, int server_type) {
    User& user = users[user_id];
    int latency = user.latency[server_type];
    int k = server_types[server_type].k;
    int memory = server_types[server_type].m;
    
    // 延迟分数（越小越好）
    double latency_score = 1.0 / (latency + 1);
    
    // 处理能力分数（越大越好）
    double capacity_score = k * sqrt(min(1000, (memory - b) / a));
    
    // 显存充裕度分数
    int max_batch = min(1000, (memory - b) / a);
    double memory_score = max_batch / 1000.0;
    
    // 时间窗口匹配度
    int time_window = user.e - user.s;
    int min_required_time = user.cnt * latency; // 最少需要的时间
    double time_score = (time_window > min_required_time) ? 1.0 : 0.1;
    
    return latency_score * capacity_score * memory_score * time_score;
}

// 改进的NPU分配策略
void assign_users_to_npus() {
    // 为每个用户选择最优服务器类型
    for (User& user : users) {
        int best_server_type = 0;
        double best_fitness = -1;
        
        for (int st = 0; st < N; st++) {
            double fitness = compute_server_fitness(user.id, st);
            if (fitness > best_fitness) {
                best_fitness = fitness;
                best_server_type = st;
            }
        }
        user.assigned_server_type = best_server_type;
    }
    
    // 初始化NPU负载
    for (NPUState& npu : npus) {
        npu.current_load = 0;
    }
    
    // 按样本量降序排序用户
    vector<User*> sorted_users;
    for (int i = 0; i < M; i++) {
        sorted_users.push_back(&users[i]);
    }
    sort(sorted_users.begin(), sorted_users.end(), [](User* a, User* b) {
        return a->cnt > b->cnt;
    });
    
    // 为每个用户分配具体的NPU
    for (User* user : sorted_users) {
        int st = user->assigned_server_type;
        
        // 找到该服务器类型中负载最轻的NPU
        int best_npu_idx = -1;
        double min_load = INF;
        
        for (int i = 0; i < npus.size(); i++) {
            if (npus[i].server_type == st && npus[i].current_load < min_load) {
                min_load = npus[i].current_load;
                best_npu_idx = i;
            }
        }
        
        if (best_npu_idx != -1) {
            user->assigned_npu = best_npu_idx;
            npus[best_npu_idx].current_load += user->cnt;
        }
    }
}

// 生成初始调度计划
void generate_initial_schedule() {
    for (User& user : users) {
        int st = user.assigned_server_type;
        int npu_idx = user.assigned_npu;
        int B_max = min(server_types[st].B_max, 1000);
        int latency = user.latency[st];
        int T_i = (user.cnt + B_max - 1) / B_max; // 向上取整
        
        user.send_times.clear();
        user.batches.clear();
        user.server_types.clear();
        user.npu_indices.clear();
        
        // 严格的通信时延约束处理
        int current_send_time = user.s;
        int last_arrival_time = -1;
        
        // 生成发送时间序列
        for (int i = 0; i < T_i; i++) {
            // 确保满足通信约束：只有在上个请求到达后才能发送下个请求
            if (last_arrival_time != -1) {
                current_send_time = max(current_send_time, last_arrival_time + 1);
            }
            
            user.send_times.push_back(current_send_time);
            user.server_types.push_back(st);
            user.npu_indices.push_back(npus[user.assigned_npu].npu_index);
            
            // 更新到达时间
            last_arrival_time = current_send_time + latency;
            
            // 为下次发送预留时间
            current_send_time = last_arrival_time + 1;
        }
        
        // 设置批次大小
        int remaining = user.cnt;
        for (int i = 0; i < T_i - 1; i++) {
            int batch = min(B_max, remaining);
            user.batches.push_back(batch);
            remaining -= batch;
        }
        user.batches.push_back(remaining);
    }
}

// 事件驱动模拟（（考虑用于后续迭代优化））
void event_driven_simulation() {
    priority_queue<Event, vector<Event>, greater<Event>> events;
    vector<Request*> all_requests;
    
    // 初始化发送事件
    for (User& user : users) {
        int last_send_end = -1; // 上次发送完成时间（发送时间 + 通信时延）
        
        for (int i = 0; i < user.send_times.size(); i++) {
            Request* req = new Request();
            req->user_id = user.id;
            req->batch = user.batches[i];
            req->server_type = user.server_types[i];
            req->npu_index = user.npu_indices[i];
            req->send_time = user.send_times[i];
            
            // 确保满足通信时延约束
            if (last_send_end != -1) {
                // 最小允许发送时间 = 上次发送完成时间 + 1
                int min_send_time = last_send_end + 1;
                req->send_time = max(req->send_time, min_send_time);
            }
            
            // 计算到达时间和下次允许发送时间
            req->arrive_time = req->send_time + user.latency[req->server_type];
            last_send_end = req->send_time + user.latency[req->server_type];
            
            req->mem_used = compute_memory(req->batch);
            
            all_requests.push_back(req);
            
            Event send_event;
            send_event.time = req->send_time;
            send_event.type = SEND;
            send_event.req = req;
            events.push(send_event);
        }
    }
    
    // 重置NPU状态
    for (NPUState& npu : npus) {
        npu.free_mem = server_types[npu.server_type].m;
        while (!npu.waiting_queue.empty()) npu.waiting_queue.pop();
    }
    
    // 重置用户完成时间
    for (User& user : users) {
        user.last_finish = -1;
    }
    
    // 事件循环
    while (!events.empty()) {
        Event event = events.top();
        events.pop();
        
        if (event.type == SEND) {
            // 创建到达事件（添加通信时延）
            Event arrive_event;
            arrive_event.time = event.req->arrive_time;
            arrive_event.type = ARRIVE;
            arrive_event.req = event.req;
            
            // 找到对应的NPU
            for (NPUState& npu : npus) {
                if (npu.server_type == event.req->server_type && 
                    npu.npu_index == event.req->npu_index) {
                    arrive_event.npu = &npu;
                    break;
                }
            }
            
            events.push(arrive_event);
        } 
        else if (event.type == ARRIVE) {
            // 加入等待队列
            event.npu->waiting_queue.push(event.req);
            
            // 尝试分配资源
            if (event.npu->free_mem >= event.req->mem_used) {
                // 分配资源
                Request* req = event.npu->waiting_queue.front();
                event.npu->waiting_queue.pop();
                
                req->start_time = event.time;
                int k = server_types[req->server_type].k;
                req->finish_time = req->start_time + compute_process_time(req->batch, k);
                
                event.npu->free_mem -= req->mem_used;
                
                // 创建完成事件
                Event finish_event;
                finish_event.time = req->finish_time;
                finish_event.type = FINISH;
                finish_event.req = req;
                finish_event.npu = event.npu;
                events.push(finish_event);
            }
        } 
        else if (event.type == FINISH) {
            // 释放资源
            event.npu->free_mem += event.req->mem_used;
            
            // 更新用户完成时间
            User& user = users[event.req->user_id];
            if (event.time > user.last_finish) {
                user.last_finish = event.time;
            }
            
            // 尝试分配等待队列中的请求
            while (!event.npu->waiting_queue.empty()) {
                Request* next_req = event.npu->waiting_queue.front();
                if (event.npu->free_mem >= next_req->mem_used) {
                    event.npu->waiting_queue.pop();
                    
                    next_req->start_time = event.time;
                    int k = server_types[next_req->server_type].k;
                    next_req->finish_time = next_req->start_time + 
                                           compute_process_time(next_req->batch, k);
                    
                    event.npu->free_mem -= next_req->mem_used;
                    
                    // 创建完成事件
                    Event new_finish;
                    new_finish.time = next_req->finish_time;
                    new_finish.type = FINISH;
                    new_finish.req = next_req;
                    new_finish.npu = event.npu;
                    events.push(new_finish);
                } else {
                    break;
                }
            }
        }
    }
    
    // 清理请求内存
    for (Request* req : all_requests) {
        delete req;
    }
}

// 为单个用户重新生成调度计划
void generate_initial_schedule_for_user(User& user) {
    int st = user.assigned_server_type;
    int latency = user.latency[st];
    
    user.send_times.clear();
    user.batches.clear();
    user.server_types.clear();
    user.npu_indices.clear();
    
    int optimal_batch = compute_optimal_batch_size(st, user.cnt);
    int total_batches = (user.cnt + optimal_batch - 1) / optimal_batch;
    
    int current_send_time = user.s;
    int last_arrive_time = -1;
    
    for (int i = 0; i < total_batches; i++) {
        if (last_arrive_time != -1) {
            current_send_time = max(current_send_time, last_arrive_time + 1);
        }
        
        user.send_times.push_back(current_send_time);
        user.server_types.push_back(st);
        user.npu_indices.push_back(npus[user.assigned_npu].npu_index);
        
        int remaining = user.cnt;
        for (int j = 0; j < i; j++) {
            remaining -= user.batches[j];
        }
        int current_batch = min(optimal_batch, remaining);
        user.batches.push_back(current_batch);
        
        last_arrive_time = current_send_time + latency;
        int process_time = compute_process_time(current_batch, server_types[st].k);
        current_send_time += max(latency + 1, process_time / 10);
    }
}

// 改进的超时用户调整策略
void adjust_overtime_users() {
    bool has_overtime = false;
    
    for (User& user : users) {
        if (user.last_finish > user.e) {
            has_overtime = true;
            
            // 策略1: 尝试减少批次大小以减少排队时间
            bool adjusted = false;
            for (int& batch : user.batches) {
                if (batch > 1) {
                    batch = max(1, (int)(batch * 0.8));
                    adjusted = true;
                }
            }
            
            // 策略2: 如果批次调整无效，尝试迁移到更好的服务器
            if (!adjusted || user.last_finish > user.e) {
                // 寻找延迟更小的服务器类型
                int best_st = user.assigned_server_type;
                for (int st = 0; st < N; st++) {
                    if (user.latency[st] < user.latency[best_st]) {
                        best_st = st;
                    }
                }
                
                if (best_st != user.assigned_server_type) {
                    // 更新负载统计
                    npus[user.assigned_npu].current_load -= user.cnt;
                    
                    user.assigned_server_type = best_st;
                    user.move_count++;
                    
                    // 重新分配NPU
                    int best_npu_idx = -1;
                    double min_load = INF;
                    for (int i = 0; i < npus.size(); i++) {
                        if (npus[i].server_type == best_st && npus[i].current_load < min_load) {
                            min_load = npus[i].current_load;
                            best_npu_idx = i;
                        }
                    }
                    
                    if (best_npu_idx != -1) {
                        user.assigned_npu = best_npu_idx;
                        npus[best_npu_idx].current_load += user.cnt;
                        
                        // 重新生成调度计划
                        generate_initial_schedule_for_user(user);
                    }
                }
            }
        }
    }
}

// 计算评分(调试用)
double compute_score() {
    int K = 0; // 超时用户数
    double total = 0.0;
    
    for (User& user : users) {
        if (user.last_finish > user.e) {
            K++;
        }
    }
    
    double hK = pow(2, -K / 100.0);
    
    for (User& user : users) {
        double time_diff = max(0, user.last_finish - user.e);
        double hTime = pow(2, -time_diff / (user.e - user.s) / 100.0);
        double pMove = pow(2, -user.move_count / 200.0);
        total += hTime * pMove;
    }
    
    return hK * total * 10000;
}

// 输出结果 
void output_solution() {
    for (int i = 0; i < M; i++) {
        User& user = users[i];
        cout << user.send_times.size() << endl;
        
        for (int j = 0; j < user.send_times.size(); j++) {
            cout << user.send_times[j] << " "
                 << user.server_types[j] + 1 << " "
                 << user.npu_indices[j] + 1 << " "
                 << user.batches[j];
            
            if (j < user.send_times.size() - 1) {
                cout << " ";
            }
        }
        cout << endl;
    }
}

// 主函数
int main() {
    
    cin >> N;
    server_types.resize(N);
    for (int i = 0; i < N; i++) {
        cin >> server_types[i].g >> server_types[i].k >> server_types[i].m;
    }
    
    cin >> M;
    users.resize(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].id = i;
        users[i].move_count = 0;
    }
    
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> users[j].latency[i];
        }
    }
    
    cin >> a >> b;
    
    // 计算最大batchsize
    for (int i = 0; i < N; i++) {
        server_types[i].B_max = min(1000, (server_types[i].m - b) / a);
    }
    
    initialize_npus();
    
    assign_users_to_npus();

    generate_initial_schedule();
    
    // 最终输出
    output_solution();
  
    return 0;
}